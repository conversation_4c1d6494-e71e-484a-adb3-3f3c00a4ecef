@using Microsoft.AspNetCore.Components.Rendering
@inject IJSRuntime JSRuntime
@inject ISnackbar Snackbar
@inject HttpClient HttpClient

<MudDialog>
    <DialogContent>
        <div class="d-flex align-center mb-4">
            <MudIcon Icon="@Icons.Material.Filled.Assignment" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
            <div>
                <MudText Typo="Typo.h5" Color="Color.Primary">学生答案预览</MudText>
                <MudText Typo="Typo.body2" Color="Color.Info">@Submission?.StudentName (@Submission?.StudentNumber)</MudText>
            </div>
        </div>

        @if (loading)
        {
            <div class="d-flex justify-center align-center" style="height: 400px;">
                <MudProgressCircular Indeterminate="true" Size="Size.Large" />
                <MudText Class="ml-3">正在加载预览...</MudText>
            </div>
        }
        else if (Submission?.Files?.Any() != true)
        {
            <MudAlert Severity="Severity.Warning" Class="mb-4">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Warning" Class="mr-2" />
                    <div>
                        <MudText Typo="Typo.body1" Class="font-weight-medium">没有找到答案文件</MudText>
                        <MudText Typo="Typo.body2">该提交没有关联的文件</MudText>
                    </div>
                </div>
            </MudAlert>
        }
        else
        {
            <!-- 提交信息卡片 -->
            <MudCard Class="mb-4" Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2" />
                            提交信息
                        </MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        @if (Submission?.Score.HasValue == true)
                        {
                            <MudChip T="string" Color="Color.Success" Icon="@Icons.Material.Filled.Star">
                                已评分：@Submission.Score.Value.ToString("F1")分
                            </MudChip>
                        }
                        else
                        {
                            <MudChip T="string" Color="Color.Warning" Icon="@Icons.Material.Filled.Schedule">
                                待评分
                            </MudChip>
                        }
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">学生信息：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Submission?.StudentName (@Submission?.StudentNumber)</MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">提交时间：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Submission?.SubmittedDate.ToString("yyyy年MM月dd日 HH:mm")</MudText>
                        </MudItem>
                        <MudItem xs="12">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.AttachFile" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">答案文件（@Submission.Files.Count 个）：</MudText>
                            </div>
                            <div class="ml-6">
                                @foreach (var file in Submission.Files.OrderBy(f => f.DisplayOrder))
                                {
                                    <div class="d-flex align-center mb-1">
                                        <MudIcon Icon="@GetFileIcon(file.FileType)" Size="Size.Small" Class="mr-2" />
                                        <MudText Typo="Typo.body2" Class="mr-2">@file.FileName</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">(@FormatFileSize(file.FileSize))</MudText>
                                    </div>
                                }
                            </div>
                        </MudItem>
                        @if (!string.IsNullOrEmpty(Submission?.Comments))
                        {
                            <MudItem xs="12">
                                <div class="d-flex align-center mb-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Comment" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                    <MudText Typo="Typo.body2" Class="font-weight-medium">评语：</MudText>
                                </div>
                                <MudPaper Class="pa-3 ml-6" Elevation="1">
                                    <MudText Typo="Typo.body2">@Submission.Comments</MudText>
                                </MudPaper>
                            </MudItem>
                        }
                    </MudGrid>
                </MudCardContent>
            </MudCard>

            <!-- 文件预览区域 -->
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.Preview" Class="mr-2" />
                            文件预览
                        </MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (Submission.Files.Count == 1)
                    {
                        <!-- 单文件直接预览 -->
                        <div style="height: 500px;">
                            @RenderFilePreview(Submission.Files.First())
                        </div>
                    }
                    else
                    {
                        <!-- 多文件标签页预览 -->
                        <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
                            @foreach (var file in Submission.Files.OrderBy(f => f.DisplayOrder))
                            {
                                <MudTabPanel Text="@file.FileName" Icon="@GetFileIcon(file.FileType)">
                                    <div style="height: 500px;">
                                        @RenderFilePreview(file)
                                    </div>
                                </MudTabPanel>
                            }
                        </MudTabs>
                    }
                </MudCardContent>
            </MudCard>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel" 
                 StartIcon="@Icons.Material.Filled.Close"
                 Color="Color.Default">
            关闭
        </MudButton>
        @if (Submission?.Files?.Any() == true)
        {
            <MudButton Color="Color.Primary"
                     OnClick="DownloadAllFiles"
                     StartIcon="@Icons.Material.Filled.Download"
                     Variant="Variant.Outlined"
                     Class="mr-2">
                逐个下载
            </MudButton>
            <MudButton Color="Color.Secondary"
                     OnClick="DownloadAsZip"
                     StartIcon="@Icons.Material.Filled.Archive"
                     Variant="Variant.Outlined">
                打包下载
            </MudButton>
        }
        @if (Submission?.Score == null)
        {
            <MudButton Color="Color.Success" 
                     OnClick="StartGrading" 
                     StartIcon="@Icons.Material.Filled.Grading"
                     Variant="Variant.Filled">
                开始评分
            </MudButton>
        }
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public SuntechApp.Components.Pages.ExamGrading.ExamSubmissionView Submission { get; set; } = null!;
    
    private bool loading = true;

    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(500); // 模拟加载
        loading = false;
        StateHasChanged();
    }

    private RenderFragment RenderFilePreview(SuntechApp.Components.Pages.ExamGrading.ExamSubmissionFileView file)
    {
        return builder =>
        {
            var fileExtension = Path.GetExtension(file.FileName).ToLower();

            if (fileExtension == ".pdf")
            {
                // PDF 预览
                builder.OpenElement(0, "iframe");
                builder.AddAttribute(1, "src", $"/api/ExamFile/preview-answer/{Path.GetFileName(file.FilePath)}#view=FitH&toolbar=1");
                builder.AddAttribute(2, "style", "width: 100%; height: 100%; border: none;");
                builder.AddAttribute(3, "title", file.FileName);
                builder.CloseElement();
            }
            else if (IsImageFile(fileExtension))
            {
                // 图片预览
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "text-center");
                builder.OpenElement(2, "img");
                builder.AddAttribute(3, "src", $"/api/ExamFile/preview-answer/{Path.GetFileName(file.FilePath)}");
                builder.AddAttribute(4, "style", "max-width: 100%; max-height: 100%; object-fit: contain;");
                builder.AddAttribute(5, "alt", file.FileName);
                builder.CloseElement();
                builder.CloseElement();
            }
            else if (IsOfficeFile(fileExtension))
            {
                // Office文档预览（转换为PDF）
                builder.OpenElement(0, "iframe");
                builder.AddAttribute(1, "src", $"/api/ExamFile/preview-office/{Path.GetFileName(file.FilePath)}#view=FitH&toolbar=1");
                builder.AddAttribute(2, "style", "width: 100%; height: 100%; border: none;");
                builder.AddAttribute(3, "title", file.FileName);
                builder.CloseElement();
            }
            else if (fileExtension == ".txt")
            {
                // 文本文件预览
                builder.OpenComponent<TextFilePreview>(0);
                builder.AddAttribute(1, "FileName", Path.GetFileName(file.FilePath));
                builder.CloseComponent();
            }
            else if (fileExtension == ".csv")
            {
                // CSV文件预览
                builder.OpenComponent<CsvFilePreview>(0);
                builder.AddAttribute(1, "FileName", Path.GetFileName(file.FilePath));
                builder.CloseComponent();
            }
            else
            {
                // 不支持预览的文件类型
                builder.OpenComponent<MudAlert>(0);
                builder.AddAttribute(1, "Severity", Severity.Warning);
                builder.AddAttribute(2, "ChildContent", (RenderFragment)(b =>
                {
                    b.OpenElement(0, "div");
                    b.AddAttribute(1, "class", "text-center");
                    b.OpenComponent<MudIcon>(2);
                    b.AddAttribute(3, "Icon", GetFileIcon(file.FileType));
                    b.AddAttribute(4, "Size", Size.Large);
                    b.AddAttribute(5, "Class", "mb-2");
                    b.CloseComponent();
                    b.OpenElement(6, "div");
                    b.AddContent(7, $"无法预览 {fileExtension.ToUpper()} 文件");
                    b.CloseElement();
                    b.OpenElement(8, "div");
                    b.AddContent(9, "请下载文件查看内容");
                    b.CloseElement();
                    b.CloseElement();
                }));
                builder.CloseComponent();
            }
        };
    }

    private bool IsImageFile(string extension)
    {
        return new[] { ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp" }.Contains(extension);
    }

    private bool IsOfficeFile(string extension)
    {
        return new[] { ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx" }.Contains(extension);
    }

    private string GetFileIcon(string? fileType)
    {
        return fileType?.ToLower() switch
        {
            "pdf" => Icons.Material.Filled.PictureAsPdf,
            "docx" or "doc" => Icons.Material.Filled.Description,
            "xlsx" or "xls" => Icons.Material.Filled.TableChart,
            "pptx" or "ppt" => Icons.Material.Filled.Slideshow,
            "png" or "jpg" or "jpeg" or "gif" or "bmp" => Icons.Material.Filled.Image,
            "zip" or "rar" or "7z" => Icons.Material.Filled.Archive,
            "txt" => Icons.Material.Filled.TextSnippet,
            "cs" or "js" or "html" or "css" or "sql" => Icons.Material.Filled.Code,
            "mp4" or "avi" or "mov" => Icons.Material.Filled.VideoFile,
            _ => Icons.Material.Filled.AttachFile
        };
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
        return $"{bytes / (1024 * 1024):F1} MB";
    }

    private async Task DownloadAllFiles()
    {
        try
        {
            if (Submission?.Files?.Any() == true)
            {
                foreach (var file in Submission.Files)
                {
                    var url = $"/api/ExamFile/download-answer/{Path.GetFileName(file.FilePath)}";
                    await JSRuntime.InvokeVoidAsync("open", url, "_blank");
                    await Task.Delay(500); // 避免同时下载太多文件
                }
                Snackbar.Add($"开始逐个下载 {Submission.Files.Count} 个文件", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"下载失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DownloadAsZip()
    {
        try
        {
            if (Submission?.Id > 0)
            {
                var url = $"/api/ExamFile/download-submission-zip/{Submission.Id}";
                await JSRuntime.InvokeVoidAsync("open", url, "_blank");
                Snackbar.Add("开始下载打包文件", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"打包下载失败: {ex.Message}", Severity.Error);
        }
    }

    private void StartGrading()
    {
        MudDialog.Close(DialogResult.Ok("start_grading"));
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}

@* 文本文件预览组件 *@
@code {
    public class TextFilePreview : ComponentBase
    {
        [Parameter] public string FileName { get; set; } = string.Empty;
        [Inject] public HttpClient HttpClient { get; set; } = null!;
        [Inject] public ISnackbar Snackbar { get; set; } = null!;

        private string? textContent;
        private bool loading = true;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                var response = await HttpClient.GetAsync($"/api/ExamFile/preview-content/{FileName}");
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<dynamic>();
                    textContent = result?.GetProperty("content").GetString();
                }
                else
                {
                    Snackbar.Add("加载文本内容失败", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"预览失败: {ex.Message}", Severity.Error);
            }
            finally
            {
                loading = false;
                StateHasChanged();
            }
        }

        protected override void BuildRenderTree(RenderTreeBuilder builder)
        {
            if (loading)
            {
                builder.OpenComponent<MudProgressCircular>(0);
                builder.AddAttribute(1, "Indeterminate", true);
                builder.CloseComponent();
            }
            else if (!string.IsNullOrEmpty(textContent))
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "style", "background: #f5f5f5; padding: 16px; border-radius: 4px; height: 100%; overflow: auto;");
                builder.OpenElement(2, "pre");
                builder.AddAttribute(3, "style", "margin: 0; white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 14px;");
                builder.AddContent(4, textContent);
                builder.CloseElement();
                builder.CloseElement();
            }
            else
            {
                builder.OpenComponent<MudAlert>(0);
                builder.AddAttribute(1, "Severity", Severity.Warning);
                builder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, "无法加载文本内容")));
                builder.CloseComponent();
            }
        }
    }

    public class CsvFilePreview : ComponentBase
    {
        [Parameter] public string FileName { get; set; } = string.Empty;
        [Inject] public HttpClient HttpClient { get; set; } = null!;
        [Inject] public ISnackbar Snackbar { get; set; } = null!;

        private List<Dictionary<string, object>>? csvData;
        private bool loading = true;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                var response = await HttpClient.GetAsync($"/api/ExamFile/preview-content/{FileName}");
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<dynamic>();
                    var dataElement = result?.GetProperty("data");
                    if (dataElement.HasValue)
                    {
                        csvData = System.Text.Json.JsonSerializer.Deserialize<List<Dictionary<string, object>>>(dataElement.Value.GetRawText());
                    }
                }
                else
                {
                    Snackbar.Add("加载CSV数据失败", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"预览失败: {ex.Message}", Severity.Error);
            }
            finally
            {
                loading = false;
                StateHasChanged();
            }
        }

        protected override void BuildRenderTree(RenderTreeBuilder builder)
        {
            if (loading)
            {
                builder.OpenComponent<MudProgressCircular>(0);
                builder.AddAttribute(1, "Indeterminate", true);
                builder.CloseComponent();
            }
            else if (csvData?.Any() == true)
            {
                builder.OpenComponent<MudTable<Dictionary<string, object>>>(0);
                builder.AddAttribute(1, "Items", csvData);
                builder.AddAttribute(2, "Dense", true);
                builder.AddAttribute(3, "Hover", true);
                builder.AddAttribute(4, "Bordered", true);
                builder.AddAttribute(5, "HeaderContent", (RenderFragment)(b =>
                {
                    var headers = csvData.First().Keys;
                    foreach (var header in headers)
                    {
                        b.OpenComponent<MudTh>(0);
                        b.AddAttribute(1, "ChildContent", (RenderFragment)(tb => tb.AddContent(0, header)));
                        b.CloseComponent();
                    }
                }));
                builder.AddAttribute(6, "RowTemplate", (RenderFragment<Dictionary<string, object>>)(row => b =>
                {
                    foreach (var kvp in row)
                    {
                        b.OpenComponent<MudTd>(0);
                        b.AddAttribute(1, "ChildContent", (RenderFragment)(tb => tb.AddContent(0, kvp.Value?.ToString() ?? "")));
                        b.CloseComponent();
                    }
                }));
                builder.CloseComponent();
            }
            else
            {
                builder.OpenComponent<MudAlert>(0);
                builder.AddAttribute(1, "Severity", Severity.Warning);
                builder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, "无法加载CSV数据")));
                builder.CloseComponent();
            }
        }
    }
}
