using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using SuntechApp.Data;
using MiniExcelLibs;
using System.Diagnostics;
using System.Text;

namespace SuntechApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ExamFileController : ControllerBase
    {
        private readonly ILogger<ExamFileController> _logger;
        private readonly string _paperStoragePath;
        private readonly string _submissionStoragePath;
        private readonly string _previewCachePath;
        private readonly string _libreOfficePath;
        private readonly ISqlSugarClient _db;
        private static readonly object _officeConvertLock = new();

        public ExamFileController(ILogger<ExamFileController> logger, Microsoft.Extensions.Configuration.IConfiguration configuration, ISqlSugarClient db)
        {
            _logger = logger;
            _db = db;
            _paperStoragePath = configuration["ExamSettings:PaperStoragePath"] ?? @"C:\ExamFiles\Papers";
            _submissionStoragePath = configuration["ExamSettings:SubmissionStoragePath"] ?? @"C:\ExamFiles\Submissions";
            _previewCachePath = Path.Combine(_submissionStoragePath, "PreviewCache");
            _libreOfficePath = configuration["IntranetResourceSite:LibreOfficePath"] ?? @"C:\Program Files\LibreOffice\program\soffice.exe";

            // 确保预览缓存目录存在
            if (!Directory.Exists(_previewCachePath))
            {
                Directory.CreateDirectory(_previewCachePath);
            }
        }

        private SqlSugarProvider DefaultDb => _db.AsTenant().GetConnection("Default");

        /// <summary>
        /// 预览试卷文件（需要登录）
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns></returns>
        [HttpGet("preview-paper/{filename}")]
        [Authorize]
        public IActionResult PreviewPaper(string filename)
        {
            try
            {
                // 验证文件名，防止路径遍历攻击
                if (string.IsNullOrEmpty(filename) || filename.Contains("..") || filename.Contains("/") || filename.Contains("\\"))
                {
                    return BadRequest("无效的文件名");
                }

                var filePath = Path.Combine(_paperStoragePath, filename);

                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("文件不存在");
                }

                var fileBytes = System.IO.File.ReadAllBytes(filePath);
                var contentType = GetContentType(filename);

                _logger.LogInformation("试卷文件预览: {FileName}, 用户: {User}", filename, User.Identity?.Name);

                // 设置为内联显示，而不是下载
                Response.Headers.Add("Content-Disposition", $"inline; filename=\"{filename}\"");

                return File(fileBytes, contentType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预览试卷文件失败: {FileName}", filename);
                return StatusCode(500, "预览失败");
            }
        }

        /// <summary>
        /// 下载试卷文件（无需登录）
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns></returns>
        [HttpGet("download-paper/{filename}")]
        public IActionResult DownloadPaper(string filename)
        {
            try
            {
                // 验证文件名，防止路径遍历攻击
                if (string.IsNullOrEmpty(filename) || filename.Contains("..") || filename.Contains("/") || filename.Contains("\\"))
                {
                    return BadRequest("无效的文件名");
                }

                var filePath = Path.Combine(_paperStoragePath, filename);

                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("文件不存在");
                }

                var fileBytes = System.IO.File.ReadAllBytes(filePath);
                var contentType = GetContentType(filename);

                // 获取原始文件名（这里简化处理，实际应该从数据库获取）
                var downloadName = filename;

                _logger.LogInformation("试卷文件下载: {FileName}", filename);

                return File(fileBytes, contentType, downloadName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载试卷文件失败: {FileName}", filename);
                return StatusCode(500, "下载失败");
            }
        }

        /// <summary>
        /// 预览答案文件（需要登录）
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns></returns>
        [HttpGet("preview-answer/{filename}")]
        [Authorize]
        public async Task<IActionResult> PreviewAnswer(string filename)
        {
            try
            {
                // 验证文件名，防止路径遍历攻击
                if (string.IsNullOrEmpty(filename) || filename.Contains("..") || filename.Contains("/") || filename.Contains("\\"))
                {
                    return BadRequest("无效的文件名");
                }

                var filePath = Path.Combine(_submissionStoragePath, filename);

                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("文件不存在");
                }

                var fileBytes = System.IO.File.ReadAllBytes(filePath);
                var contentType = GetContentType(filename);

                _logger.LogInformation("答案文件预览: {FileName}, 用户: {User}", filename, User.Identity?.Name);

                // 设置为内联显示，而不是下载
                Response.Headers.Add("Content-Disposition", $"inline; filename=\"{filename}\"");

                return File(fileBytes, contentType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预览答案文件失败: {FileName}", filename);
                return StatusCode(500, "预览失败");
            }
        }

        /// <summary>
        /// 下载答案文件（需要登录）
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns></returns>
        [HttpGet("download-answer/{filename}")]
        [Authorize]
        public async Task<IActionResult> DownloadAnswer(string filename)
        {
            try
            {
                // 验证文件名，防止路径遍历攻击
                if (string.IsNullOrEmpty(filename) || filename.Contains("..") || filename.Contains("/") || filename.Contains("\\"))
                {
                    return BadRequest("无效的文件名");
                }

                var filePath = Path.Combine(_submissionStoragePath, filename);

                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("文件不存在");
                }

                var fileBytes = System.IO.File.ReadAllBytes(filePath);
                var contentType = GetContentType(filename);

                // 从数据库获取原始文件名
                var submissionFile = await DefaultDb.Queryable<ExamSubmissionFile>()
                    .Where(f => f.FilePath.Contains(filename))
                    .FirstAsync();

                var downloadName = submissionFile?.FileName ?? filename;

                _logger.LogInformation("答案文件下载: {FileName}, 用户: {User}", filename, User.Identity?.Name);

                return File(fileBytes, contentType, downloadName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载答案文件失败: {FileName}", filename);
                return StatusCode(500, "下载失败");
            }
        }

        /// <summary>
        /// 批量下载提交的所有文件（ZIP格式）
        /// </summary>
        /// <param name="submissionId">提交ID</param>
        /// <returns></returns>
        [HttpGet("download-submission-zip/{submissionId}")]
        [Authorize]
        public async Task<IActionResult> DownloadSubmissionZip(int submissionId)
        {
            try
            {
                // 获取提交记录和文件列表
                var submission = await DefaultDb.Queryable<ExamSubmission>()
                    .Where(s => s.Id == submissionId)
                    .FirstAsync();

                if (submission == null)
                {
                    return NotFound("提交记录不存在");
                }

                var files = await DefaultDb.Queryable<ExamSubmissionFile>()
                    .Where(f => f.SubmissionId == submissionId)
                    .OrderBy(f => f.DisplayOrder)
                    .ToListAsync();

                if (!files.Any())
                {
                    return NotFound("没有找到文件");
                }

                // 创建ZIP文件
                using var memoryStream = new MemoryStream();
                using (var archive = new System.IO.Compression.ZipArchive(memoryStream, System.IO.Compression.ZipArchiveMode.Create, true))
                {
                    foreach (var file in files)
                    {
                        var filePath = file.FilePath;
                        if (System.IO.File.Exists(filePath))
                        {
                            var entry = archive.CreateEntry(file.FileName);
                            using var entryStream = entry.Open();
                            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                            await fileStream.CopyToAsync(entryStream);
                        }
                    }
                }

                var zipFileName = $"{submission.StudentName}_{submission.StudentNumber}_答案文件.zip";

                _logger.LogInformation("批量下载答案文件: SubmissionId={SubmissionId}, 文件数={FileCount}, 用户: {User}",
                    submissionId, files.Count, User.Identity?.Name);

                return File(memoryStream.ToArray(), "application/zip", zipFileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量下载答案文件失败: SubmissionId={SubmissionId}", submissionId);
                return StatusCode(500, "下载失败");
            }
        }

        /// <summary>
        /// 根据文件扩展名获取 Content-Type
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns></returns>
        private static string GetContentType(string filename)
        {
            var extension = Path.GetExtension(filename).ToLowerInvariant();

            return extension switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".csv" => "text/csv",
                ".txt" => "text/plain",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                ".html" => "text/html",
                ".css" => "text/css",
                ".js" => "application/javascript",
                ".json" => "application/json",
                ".xml" => "application/xml",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// 导出考试结果为 Excel
        /// </summary>
        /// <param name="examId">考试ID，为空则导出所有考试</param>
        /// <returns></returns>
        [HttpGet("export-exam-results")]
        [Authorize]
        public async Task<IActionResult> ExportExamResults(int? examId = null)
        {
            try
            {
                // 构建查询
                var query = DefaultDb.Queryable<ExamSubmission>()
                    .LeftJoin<ExamPaper>((s, p) => s.ExamPaperId == p.Id);

                // 如果指定了考试ID，则只导出该考试的结果
                if (examId.HasValue)
                {
                    query = query.Where((s, p) => s.ExamPaperId == examId.Value);
                }

                var results = await query.Select((s, p) => new
                {
                    SubmissionId = s.Id,
                    ExamTitle = p.Title,
                    StudentName = s.StudentName,
                    StudentNumber = s.StudentNumber,
                    SubmittedDate = s.SubmittedDate,
                    Score = s.Score,
                    Comments = s.Comments
                }).ToListAsync();

                // 获取文件信息
                var submissionIds = results.Select(r => r.SubmissionId).ToList();
                var files = await DefaultDb.Queryable<ExamSubmissionFile>()
                    .Where(f => submissionIds.Contains(f.SubmissionId))
                    .ToListAsync();

                if (results == null || !results.Any())
                {
                    return BadRequest("没有找到考试结果数据");
                }

                // 准备导出数据
                var exportData = results.Select(r =>
                {
                    var submissionFiles = files.Where(f => f.SubmissionId == r.SubmissionId).ToList();
                    var fileNames = submissionFiles.Any()
                        ? string.Join("; ", submissionFiles.Select(f => f.FileName))
                        : "无文件";

                    return new
                    {
                        考试名称 = r.ExamTitle,
                        学生姓名 = r.StudentName,
                        学生工号 = r.StudentNumber,
                        提交时间 = r.SubmittedDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        分数 = r.Score?.ToString("F1") ?? "未评分",
                        评语 = r.Comments ?? "",
                        答案文件 = fileNames,
                        文件数量 = submissionFiles.Count
                    };
                }).ToList();

                // 生成 Excel 文件
                var stream = new MemoryStream();
                await stream.SaveAsAsync(exportData);
                stream.Position = 0;

                // 生成文件名
                var fileName = examId.HasValue
                    ? $"考试结果_{examId.Value}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                    : $"所有考试结果_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                _logger.LogInformation("导出考试结果: {FileName}, 记录数: {Count}, 用户: {User}",
                    fileName, exportData.Count, User.Identity?.Name);

                return File(stream.ToArray(),
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出考试结果失败, ExamId: {ExamId}", examId);
                return StatusCode(500, "导出失败");
            }
        }

        /// <summary>
        /// 导出考试统计报告
        /// </summary>
        /// <returns></returns>
        [HttpGet("export-exam-statistics")]
        [Authorize]
        public async Task<IActionResult> ExportExamStatistics()
        {
            try
            {
                // 获取考试统计数据
                var examStats = await DefaultDb.Queryable<ExamPaper>()
                    .LeftJoin<ExamSubmission>((p, s) => p.Id == s.ExamPaperId)
                    .GroupBy((p, s) => new { p.Id, p.Title, p.UploadedDate, p.IsActive })
                    .Select((p, s) => new
                    {
                        ExamId = p.Id,
                        ExamTitle = p.Title,
                        UploadedDate = p.UploadedDate,
                        IsActive = p.IsActive,
                        SubmissionCount = SqlFunc.AggregateCount(s.Id),
                        GradedCount = SqlFunc.AggregateSum(SqlFunc.IIF(s.Score != null, 1, 0)),
                        AverageScore = SqlFunc.AggregateAvg(s.Score),
                        HighestScore = SqlFunc.AggregateMax(s.Score),
                        LowestScore = SqlFunc.AggregateMin(s.Score)
                    })
                    .ToListAsync();

                if (!examStats.Any())
                {
                    return BadRequest("没有找到考试统计数据");
                }

                // 计算通过率
                var exportData = new List<object>();
                foreach (var stat in examStats)
                {
                    var passRate = 0m;
                    if (stat.GradedCount > 0)
                    {
                        var passedCount = await DefaultDb.Queryable<ExamSubmission>()
                            .Where(x => x.ExamPaperId == stat.ExamId && x.Score >= 60)
                            .CountAsync();
                        passRate = (decimal)passedCount / stat.GradedCount;
                    }

                    exportData.Add(new
                    {
                        考试名称 = stat.ExamTitle,
                        上传时间 = stat.UploadedDate.ToString("yyyy-MM-dd HH:mm"),
                        状态 = stat.IsActive ? "启用" : "禁用",
                        提交人数 = stat.SubmissionCount,
                        已评分数 = stat.GradedCount,
                        完成率 = stat.SubmissionCount > 0 ? $"{(decimal)stat.GradedCount / stat.SubmissionCount:P1}" : "0%",
                        平均分 = stat.AverageScore?.ToString("F1") ?? "未评分",
                        最高分 = stat.HighestScore?.ToString("F1") ?? "未评分",
                        最低分 = stat.LowestScore?.ToString("F1") ?? "未评分",
                        通过率 = stat.GradedCount > 0 ? passRate.ToString("P1") : "未评分"
                    });
                }

                // 生成 Excel 文件
                var stream = new MemoryStream();
                await stream.SaveAsAsync(exportData);
                stream.Position = 0;

                var fileName = $"考试统计报告_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                _logger.LogInformation("导出考试统计报告: {FileName}, 考试数: {Count}, 用户: {User}",
                    fileName, exportData.Count, User.Identity?.Name);

                return File(stream.ToArray(),
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出考试统计报告失败");
                return StatusCode(500, "导出失败");
            }
        }

        /// <summary>
        /// 获取文件内容用于预览（支持文本和CSV）
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns></returns>
        [HttpGet("preview-content/{filename}")]
        [Authorize]
        public async Task<IActionResult> PreviewFileContent(string filename)
        {
            try
            {
                // 验证文件名
                if (string.IsNullOrEmpty(filename) || filename.Contains("..") || filename.Contains("/") || filename.Contains("\\"))
                {
                    return BadRequest("无效的文件名");
                }

                var filePath = Path.Combine(_submissionStoragePath, filename);
                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("文件不存在");
                }

                var extension = Path.GetExtension(filename).ToLowerInvariant();

                // 处理文本文件
                if (extension == ".txt")
                {
                    var content = await ReadTextFileAsync(filePath);
                    return Ok(new { type = "text", content });
                }

                // 处理CSV文件
                if (extension == ".csv")
                {
                    var data = await ReadCsvFileAsync(filePath);
                    return Ok(new { type = "csv", data });
                }

                return BadRequest("不支持的文件类型");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预览文件内容失败: {FileName}", filename);
                return StatusCode(500, "预览失败");
            }
        }

        /// <summary>
        /// 预览Office文档（转换为PDF）
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns></returns>
        [HttpGet("preview-office/{filename}")]
        [Authorize]
        public async Task<IActionResult> PreviewOfficeDocument(string filename)
        {
            try
            {
                // 验证文件名
                if (string.IsNullOrEmpty(filename) || filename.Contains("..") || filename.Contains("/") || filename.Contains("\\"))
                {
                    return BadRequest("无效的文件名");
                }

                var filePath = Path.Combine(_submissionStoragePath, filename);
                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("文件不存在");
                }

                var extension = Path.GetExtension(filename).ToLowerInvariant();
                var officeExtensions = new[] { ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx" };

                if (!officeExtensions.Contains(extension))
                {
                    return BadRequest("不是Office文档");
                }

                // 转换为PDF
                var pdfPath = await ConvertOfficeToPdfAsync(filePath);
                if (string.IsNullOrEmpty(pdfPath) || !System.IO.File.Exists(pdfPath))
                {
                    return StatusCode(500, "文档转换失败");
                }

                var pdfBytes = await System.IO.File.ReadAllBytesAsync(pdfPath);
                Response.Headers.Add("Content-Disposition", $"inline; filename=\"{Path.GetFileNameWithoutExtension(filename)}.pdf\"");

                _logger.LogInformation("Office文档预览: {FileName}, 用户: {User}", filename, User.Identity?.Name);

                return File(pdfBytes, "application/pdf");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预览Office文档失败: {FileName}", filename);
                return StatusCode(500, "预览失败");
            }
        }

        private async Task<string> ReadTextFileAsync(string filePath)
        {
            // 尝试不同编码读取文本文件
            var encodings = new[] { Encoding.UTF8, Encoding.GetEncoding("GB2312"), Encoding.Default };

            foreach (var encoding in encodings)
            {
                try
                {
                    var content = await System.IO.File.ReadAllTextAsync(filePath, encoding);
                    if (!content.Contains('�')) // 简单检测乱码
                    {
                        return content;
                    }
                }
                catch
                {
                    continue;
                }
            }

            return await System.IO.File.ReadAllTextAsync(filePath, Encoding.UTF8);
        }

        private async Task<List<Dictionary<string, object>>> ReadCsvFileAsync(string filePath)
        {
            var result = new List<Dictionary<string, object>>();
            var lines = await System.IO.File.ReadAllLinesAsync(filePath, Encoding.UTF8);

            if (lines.Length == 0) return result;

            // 解析CSV头部
            var headers = ParseCsvLine(lines[0]);

            // 解析数据行（限制前50行避免性能问题）
            var dataLines = lines.Skip(1).Take(50);

            foreach (var line in dataLines)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                var values = ParseCsvLine(line);
                var row = new Dictionary<string, object>();

                for (int i = 0; i < headers.Length && i < values.Length; i++)
                {
                    row[headers[i]] = values[i];
                }

                result.Add(row);
            }

            return result;
        }

        private async Task<string?> ConvertOfficeToPdfAsync(string inputFile)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 生成缓存文件名
                    var fileInfo = new FileInfo(inputFile);
                    var cacheFileName = $"{Path.GetFileNameWithoutExtension(inputFile)}_{fileInfo.LastWriteTime:yyyyMMddHHmmss}.pdf";
                    var cachedPdfPath = Path.Combine(_previewCachePath, cacheFileName);

                    // 检查缓存是否存在
                    if (System.IO.File.Exists(cachedPdfPath))
                    {
                        return cachedPdfPath;
                    }

                    // 执行转换
                    ConvertOfficeToPdf(inputFile);

                    // 查找转换后的PDF文件
                    var expectedPdfPath = Path.ChangeExtension(inputFile, ".pdf");
                    if (System.IO.File.Exists(expectedPdfPath))
                    {
                        // 移动到缓存目录
                        System.IO.File.Move(expectedPdfPath, cachedPdfPath);
                        return cachedPdfPath;
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Office文档转PDF失败: {InputFile}", inputFile);
                    return null;
                }
            });
        }

        private void ConvertOfficeToPdf(string inputFile)
        {
            lock (_officeConvertLock)
            {
                var outputDir = Path.GetDirectoryName(inputFile);

                var process = new Process();
                process.StartInfo.FileName = _libreOfficePath;
                process.StartInfo.Arguments = $"--headless --convert-to pdf --outdir \"{outputDir}\" \"{inputFile}\"";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.WorkingDirectory = Path.GetDirectoryName(_libreOfficePath);

                process.Start();

                if (!process.WaitForExit(120000)) // 2分钟超时
                {
                    process.Kill();
                    throw new TimeoutException("Office文档转换超时");
                }

                if (process.ExitCode != 0)
                {
                    var error = process.StandardError.ReadToEnd();
                    throw new InvalidOperationException($"LibreOffice转换失败: {error}");
                }
            }
        }

        private static string[] ParseCsvLine(string line)
        {
            return line.Split(',').Select(s => s.Trim().Trim('"')).ToArray();
        }
    }
}
