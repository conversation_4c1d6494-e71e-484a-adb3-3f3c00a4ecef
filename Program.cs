using Hangfire;
using Hangfire.Dashboard;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using MudBlazor;
using MudBlazor.Services;
using MudBlazor.Translations;
using Serilog;
using Serilog.Events;
using SqlSugar;
using SuntechApp.Bpm;
using SuntechApp.Components;
using SuntechApp.Components.Account;
using SuntechApp.Components.Pages;
using SuntechApp.Data;
using SuntechApp.IpgPlugin;
using SuntechApp.ITS;

var builder = WebApplication.CreateBuilder(args);

// Add MudBlazor services
builder.Services.AddMudServices();
builder.Services.AddMudTranslations();
builder.Services.AddMudMarkdownServices();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();

builder.Services.AddAuthentication(options =>
    {
        options.DefaultScheme = IdentityConstants.ApplicationScheme;
        options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
    })
    .AddIdentityCookies();

var connectionString = builder.Configuration.GetConnectionString("Default") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString));
builder.Services.AddDatabaseDeveloperPageExceptionFilter();

builder.Services.AddIdentityCore<ApplicationUser>(options =>
{
    options.SignIn.RequireConfirmedAccount = true;
    options.SignIn.RequireConfirmedAccount = false; // 取消邮箱验证要求
    options.Password.RequireDigit = false;         // 降低密码策略
})
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddSignInManager()
    .AddDefaultTokenProviders();
builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();
builder.Services.AddRazorPages();
//---------------------------------以上为Blazor模板配置-------------------------
builder.Services.Configure<IpgJsonSettings>(builder.Configuration.GetSection("IpgJsonSettings"));
builder.Services.Configure<RptSettings>(builder.Configuration.GetSection("RptSettings"));
builder.Services.Configure<ITSEmailSettings>(builder.Configuration.GetSection("ITSEmailSettings"));
builder.Services.Configure<IntranetResourceSite>(builder.Configuration.GetSection("IntranetResourceSite"));

builder.Services.AddHangfire(configuration => configuration
       .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
       .UseSimpleAssemblyNameTypeSerializer()
       .UseRecommendedSerializerSettings()
       .UseSqlServerStorage(builder.Configuration.GetConnectionString("Default")));
builder.Services.AddHangfireServer(options =>
{
    options.WorkerCount = 3; //Environment.ProcessorCount * 1;
    options.Queues = ["alpha"];
});
builder.Services.AddHangfireServer(options =>
{
    options.WorkerCount = 3;
    options.Queues = ["default"];
});    

builder.Services.AddDbContextFactory<SuntechAppDbContext>(options =>
{
    var connectionString = builder.Configuration.GetConnectionString("Default");
    options.UseSqlServer(connectionString);
});
builder.Services.AddDbContextFactory<RptDbContext>(options =>
{
    var connectionString = builder.Configuration.GetConnectionString("RPT");
    options.UseSqlServer(connectionString);
});

Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .MinimumLevel.Override("System", LogEventLevel.Warning)
    .Filter.ByExcluding(logEvent =>
        logEvent.Exception != null &&
        logEvent.Exception.Message.Contains("JavaScript interop calls cannot be issued at this time"))
    .WriteTo.File("logs/app.log",
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {SourceContext}: {Message}{NewLine}{Exception}",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30)
    .CreateLogger();
builder.Host.UseSerilog();

builder.Services.AddScoped<ISqlSugarClient>(s =>
{
    SqlSugarScope sqlSugar = new (
    [
        new()
        {
            ConfigId="BPMDATA",
            DbType = SqlSugar.DbType.SqlServer,
            ConnectionString = builder.Configuration.GetConnectionString("BPMDATA"),
            IsAutoCloseConnection = true
        },
        new()
        {
            ConfigId="RPT",
            DbType = SqlSugar.DbType.SqlServer,
            ConnectionString = builder.Configuration.GetConnectionString("RPT"),
            IsAutoCloseConnection = true
        },
        new()
        {
            ConfigId="Default",
            DbType = SqlSugar.DbType.SqlServer,
            ConnectionString = builder.Configuration.GetConnectionString("Default"),
            IsAutoCloseConnection = true
        }
    ],
    db =>
    {
        db.Aop.OnLogExecuting = (sql, pars) =>
        {

        };
    });
    return sqlSugar;
});

builder.Services.AddScoped<EmployeeService>();
builder.Services.AddScoped<TasksService>();
builder.Services.AddKeyedScoped<IBpmService, BpmService>("BpmService");
builder.Services.AddScoped<TaskDetailsService>();
builder.Services.AddScoped<DailySalesReportService>();
builder.Services.AddSingleton<EncryptedBPMAttFileService>();
builder.Services.AddSingleton<DecryptFilesService>();
builder.Services.AddSingleton<EncryptFilesService>();
builder.Services.AddScoped<ITSEmailServices>();
builder.Services.AddScoped<ValueDataMap>();
builder.Services.AddScoped<DialogBase>();
builder.Services.AddScoped(typeof(IRptRepository<,>), typeof(RptRepository<,>));
builder.Services.AddControllers();
builder.Services.AddHttpClient(); // 添加默认HttpClient供组件使用
builder.Services.AddHttpClient("Client", client =>
{
    client.Timeout = TimeSpan.FromMinutes(25);
})
.ConfigurePrimaryHttpMessageHandler(client =>
{
    var handler = new HttpClientHandler
    {
        UseDefaultCredentials = true
    };
    return handler;
});
var trainingMaterialPath = builder.Configuration["IntranetResourceSite:TrainingMaterialPath"];

var app = builder.Build();

// 自动创建考试系统目录
try
{
    var paperStoragePath = builder.Configuration["ExamSettings:PaperStoragePath"] ?? @"C:\ExamFiles\Papers";
    var submissionStoragePath = builder.Configuration["ExamSettings:SubmissionStoragePath"] ?? @"C:\ExamFiles\Submissions";

    if (!Directory.Exists(paperStoragePath))
    {
        Directory.CreateDirectory(paperStoragePath);
        app.Logger.LogInformation("已创建试卷存储目录: {Path}", paperStoragePath);
    }

    if (!Directory.Exists(submissionStoragePath))
    {
        Directory.CreateDirectory(submissionStoragePath);
        app.Logger.LogInformation("已创建答案存储目录: {Path}", submissionStoragePath);
    }
}
catch (Exception ex)
{
    app.Logger.LogError(ex, "创建考试系统目录失败");
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseMigrationsEndPoint();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
app.UseHangfireDashboard("/hangfire", new DashboardOptions
{
    Authorization = [new MyAuthorizationFilter()]
});
app.UseRouting();
app.UseAuthorization();
app.UseHttpsRedirection();
app.UseAntiforgery();
app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

// Add additional endpoints required by the Identity /Account Razor components.
app.MapAdditionalIdentityEndpoints();


app.MapRazorPages()
   .WithStaticAssets();
app.UseEndpoints(endpoints =>
{
    _ = endpoints.MapControllers();  // 启用控制器路由
});
app.UseStaticFiles();
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(@"C:\12"),
    RequestPath = "/files"
});
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(trainingMaterialPath!),
    RequestPath = "/TrainingMaterial"
});
app.Run();
public class MyAuthorizationFilter : IDashboardAuthorizationFilter
{
    public bool Authorize(DashboardContext context)
    {
        var httpContext = context.GetHttpContext();

        // Allow all authenticated users to see the Dashboard (potentially dangerous).
        return httpContext.User.Identity?.IsAuthenticated ?? false;
    }
}